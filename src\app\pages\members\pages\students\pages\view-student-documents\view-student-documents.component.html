<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isPDFViewerSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="xl-sidebar"
    [disableClose]="true">
    @if (isPDFViewerSideNavOpen) {
      <app-pdf-viewer
        [documentInfo]="documentInfo"
        [isPlanRenewal]="documentInfo.isPlanRenewal"
        (closeSideNav)="isPDFViewerSideNavOpen = false"></app-pdf-viewer>
    }
  </mat-sidenav>
</mat-sidenav-container>

<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="back-btn-wrapper">
      <img [src]="constants.staticImages.icons.arrowLeft" class="pointer" (click)="closeViewAllDocument()" alt="" />
      <div class="ps-2">
        <div class="title">Documents</div>
        <div class="name">
          <img [src]="constants.staticImages.icons.profileIcon" class="pe-1" alt="" />
          {{ selectedStudentDetails?.firstName | titlecase }}
          {{ selectedStudentDetails?.lastName | titlecase }}
        </div>
      </div>
    </div>
    <div class="action-btn-wrapper">
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="closeViewAllDocument()">
        Close
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : documentList"></ng-container>
  </div>
</div>

<ng-template #documentList>
  <div class="document-list">
    @for (signedDocument of studentDocuments; track $index) {
      <div class="o-card mb-2">
        <div class="o-card-body">
          <div>
            <div class="title">{{ signedDocument.signedDocuments.documentName }}</div>
            <div class="document-content">
              <div>
                Recieved on: {{ signedDocument.signedDocuments.uploadDate | localDate | date }} -
                {{ signedDocument.signedDocuments.uploadDate | localDate | date: "shortTime" }}
              </div>
              <div class="dot"></div>
              <div>
                Agreements:
                <span [ngClass]="{ pending: !signedDocument.signedDocuments.isAgreementDone }">{{
                  signedDocument.signedDocuments.isAgreementDone ? "Agreed" : "Pending Action"
                }}</span>
              </div>
              <div class="dot"></div>
              <div class="text-black">
                Sent by <span class="primary-color">{{ signedDocument.signedDocuments.documentSendBy }}</span>
              </div>
            </div>
          </div>
          <div>
            <button
              mat-raised-button
              color="primary"
              class="mat-primary-btn action-btn"
              type="button"
              (click)="openPDFViewer(signedDocument.signedDocuments)">
              <ng-container
                *ngIf="
                  signedDocument.signedDocuments.isAgreementDone && (signedDocument.signedDocuments.isPaid || signedDocument.signedDocuments.isPlanRenewal);
                  else payment
                "
                >View Pdf</ng-container
              >
              <ng-template #payment>
                <ng-container
                  *ngIf="
                    signedDocument.signedDocuments.isAgreementDone && !signedDocument.signedDocuments.isPaid;
                    else acceptTC
                  "
                  >Payment</ng-container
                >
              </ng-template>
              <ng-template #acceptTC>Accept T&C</ng-template>
            </button>
          </div>
        </div>
      </div>
    }
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
