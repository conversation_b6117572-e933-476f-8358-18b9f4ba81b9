import { ChangeDetectorRef, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ScheduleDayViewComponent } from './pages/schedule-day-view/schedule-day-view.component';
import { MbscCalendarEvent, MbscResource } from '@mobiscroll/angular';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule, DatePipe } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule } from '@angular/forms';
import { ScheduleListViewComponent } from './pages/schedule-list-view/schedule-list-view.component';
import { ScheduleWeekViewComponent } from './pages/schedule-week-view/schedule-week-view.component';
import { SchedulerService } from './services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { CBGetResponse, CBResponse, IdNameModel } from 'src/app/shared/models';
import { SharedModule } from 'src/app/shared/shared.module';
import { InstructorList } from 'src/app/schedule-introductory-lesson/models';
import { AdvancedFilters, Filters, ClassTypes, ScheduleFilters, ScheduleDetailsView, FilterItem, ScheduleDetail } from './models';
import { OverlayModule } from '@angular/cdk/overlay';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MultiSelectComponent } from 'src/app/shared/components/multi-select/multi-select.component';
import { Instrument } from 'src/app/request-information/models';
import { MatSidenavModule } from '@angular/material/sidenav';
import { ScheduleAdvanceFilterComponent } from './pages/schedule-advance-filter/schedule-advance-filter.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { UpdateScheduleComponent } from './pages/update-schedule/update-schedule.component';
import { Debounce } from 'src/app/shared/decorators';
import { CommonService } from 'src/app/shared/services';
import { AuthService } from 'src/app/auth/services';
import { InstructorService } from 'src/app/schedule-introductory-lesson/services';
import { CommonUtils } from 'src/app/shared/utils';
import { SupervisorFilter } from 'src/app/pages/members/pages/supervisors/models';
import { ActivatedRoute, Router } from '@angular/router';
import moment from 'moment';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { LocalStorageService, StorageItem } from 'src/app/shared/services/local-storage.service';

const DEPENDENCIES = {
  MODULES: [
    MatIconModule,
    CommonModule,
    MatFormFieldModule,
    MatSelectModule,
    FormsModule,
    SharedModule,
    OverlayModule,
    FormsModule,
    MatCheckboxModule,
    MatSidenavModule,
    MatDatepickerModule,
    MatInputModule
  ],
  COMPONENTS: [
    ScheduleDayViewComponent,
    ScheduleListViewComponent,
    ScheduleWeekViewComponent,
    MultiSelectComponent,
    ScheduleAdvanceFilterComponent,
    UpdateScheduleComponent
  ]
};

@Component({
  selector: 'app-schedule',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter()],
  templateUrl: './schedule.component.html',
  styleUrl: './schedule.component.scss'
})
export class ScheduleComponent extends BaseComponent implements OnInit {
  schedulerData: MbscCalendarEvent[] = [];
  resources!: Array<MbscResource> | any;

  showScheduleForDate = new Date();
  firstDateOfCurrentWeek!: Date;
  lastDateOfCurrentWeek!: Date;
  isLoading!: boolean;
  isAdvanceFilterOpen!: boolean;
  isUpdateSchedule!: boolean;
  selectedEvent!: ScheduleDetailsView;
  appliedAdvanceFilter = new AdvancedFilters();
  originalAdvanceFIlters = new AdvancedFilters();
  locationFilterParam!: FilterItem;

  @Output() scheduleDate = new EventEmitter<Date>();
  @Output() openAddSchedule = new EventEmitter<void>();

  filterParams: Filters = {
    location: {
      id: 1,
      defaultPlaceholder: 'All Locations',
      placeholder: 'All Locations',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: false,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    instructor: {
      id: 2,
      defaultPlaceholder: 'All Instructors',
      placeholder: 'All Instructors',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    instrument: {
      id: 3,
      placeholder: 'All Instruments',
      defaultPlaceholder: 'All Instruments',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: false,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    classType: {
      id: 4,
      placeholder: 'All Class',
      defaultPlaceholder: 'All Class',
      value: new Set([1, 2, 3, 4, 5, 6]),
      totalCount: 6,
      isOpen: false,
      showSearchBar: false,
      showClassBorder: true,
      options: [
        {
          id: ClassTypes.INTRODUCTORY,
          name: 'Introductory',
          image: this.constants.staticImages.icons.introductoryLessonOptionBorder
        },
        {
          id: ClassTypes.RECURRING,
          name: 'Recurring'
        },
        {
          id: ClassTypes.GROUP_CLASS,
          name: 'Group Class',
          image: this.constants.staticImages.icons.groupClassOptionLessonBorder
        },
        {
          id: ClassTypes.ENSEMBLE_CLASS,
          name: 'Ensemble Class',
          image: this.constants.staticImages.icons.ensembleClassOptionLessonBorder
        },
        {
          id: ClassTypes.SUMMER_CAMP,
          name: 'Summer Camp',
          image: this.constants.staticImages.icons.summerCampOptionLessonBorder
        },
        {
          id: ClassTypes.MAKE_UP,
          name: 'Make-Up Lesson',
          image: this.constants.staticImages.icons.makeUpLessonOptionLessonBorder
        }
      ] as Array<IdNameModel>
    }
  };

  schedulerViews = this.constants.schedulerViews;
  selectedScheduleView = this.schedulerViews.Day;

  constructor(
    private readonly schedulerService: SchedulerService,
    private readonly localStorageService: LocalStorageService,
    private readonly cdr: ChangeDetectorRef,
    private readonly commonService: CommonService,
    private readonly instructorService: InstructorService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
    this.getScheduleDateFromQueryParams();
    this.loadSavedFilters();
    this.getFiltersListData();
  }

  getScheduleDateFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((queryParams: any) => {
      if (queryParams.date) {
        this.showScheduleForDate = new Date(queryParams.date);
        this.scheduleDate.emit(this.showScheduleForDate);
        this.removeQueryParams();
      }
    });
  }

  removeQueryParams(): void {
    this.router.navigate([], { queryParams: {}, queryParamsHandling: null });
  }

  loadSavedFilters(): void {
    const schedulerFilters = this.localStorageService.getItem(StorageItem.SchedulerFilters);
    if (schedulerFilters) {
      this.selectedScheduleView = schedulerFilters.selectedScheduleView || this.schedulerViews.Day;
      this.showScheduleForDate = new Date(schedulerFilters.maxScheduleDateFilter) || new Date();
      this.firstDateOfCurrentWeek = new Date(schedulerFilters.firstDateOfCurrentWeek) || this.firstDateOfCurrentWeek;
      this.lastDateOfCurrentWeek = new Date(schedulerFilters.lastDateOfCurrentWeek) || this.lastDateOfCurrentWeek;
      this.appliedAdvanceFilter = Object.assign(new AdvancedFilters(), schedulerFilters);
    }
  }

  saveFiltersToLocalStorage(startDate: Date, endDate: Date): void {
    const minScheduleDateFilter = DateUtils.getUtcRangeForLocalDate(startDate).startUtc;
    const maxScheduleDateFilter = DateUtils.getUtcRangeForLocalDate(endDate).endUtc;

    const persistentFilters = {
      ...this.appliedAdvanceFilter,
      selectedScheduleView: this.selectedScheduleView,
      locationIdFilter: Array.from(this.filterParams.location.value),
      instructorIdFilter: this.getInstructorFilter(),
      classTypeFilter: Array.from(this.filterParams.classType.value),
      instrumentIdFilter: Array.from(this.filterParams.instrument.value),
      maxScheduleDateFilter,
      minScheduleDateFilter,
      firstDateOfCurrentWeek: this.firstDateOfCurrentWeek,
      lastDateOfCurrentWeek: this.lastDateOfCurrentWeek
    };

    this.localStorageService.setItem(StorageItem.SchedulerFilters, persistentFilters);
  }

  initGetScheduleData(minDate: Date, maxDate: Date): void {
    this.isLoading = true;
    this.getSchedule(minDate, maxDate);
    this.saveFiltersToLocalStorage(minDate, maxDate);
  }

  @Debounce(300)
  getSchedule(minDate: Date, maxDate: Date): void {
    this.schedulerService
      .add(this.getFilterParams(minDate, maxDate), API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<ScheduleDetail>) => {
          if (res.result) {
            const scheduleEvents = res.result.scheduleLessonDetails
              ? res.result.scheduleLessonDetails.map((event: MbscCalendarEvent) => {
                  return {
                    ...event,
                    resource: event?.['instructorId'] || event?.['assignedInstructors']?.map((instructor: any) => instructor.instructorId),
                    start: DateUtils.toLocal(event.start || event['startTime']),
                    end: DateUtils.toLocal(event.end || event['endTime']),
                    scheduleDate: DateUtils.toLocal(event['scheduleDate'])
                  };
                })
              : [];

            const leaveEvents = res.result.leavesDetails
              ? res.result.leavesDetails.map((leave: any) => {
                  const fallbackStart = moment(leave.leaveDate).hour(8).minute(0).second(0).format('YYYY-MM-DDTHH:mm:ss');
                  const fallbackEnd = moment(leave.leaveDate).hour(22).minute(0).second(0).format('YYYY-MM-DDTHH:mm:ss');

                  return {
                    ...leave,
                    start: leave.leaveStartTime || fallbackStart,
                    end: leave.leaveEndTime || fallbackEnd,
                    title: `${leave.name} - OUT`,
                    color: 'rgba(0,0,0,0.4)',
                    resource: leave.instructorId,
                    isLeave: true,
                    locationName: leave.locationName,
                    id: `leave_${leave.memberRequestId}_${leave.instructorId}`
                  };
                })
              : [];

            this.schedulerData = [...scheduleEvents, ...leaveEvents];
          }

          this.locationFilterParam = { ...this.filterParams.location };
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  getInstructorFilter(): Array<number> {
    switch (this.currentUser?.userRoleId) {
      case this.constants.roleIds.INSTRUCTOR:
        return [this.currentUser?.dependentId];
      case this.constants.roleIds.SUPERVISOR:
        if (!this.filterParams.instructor.value || this.filterParams.instructor.value.size === 0) {
          return [];
        }
        if (this.filterParams.instructor.value.size === this.filterParams.instructor.totalCount) {
          return [];
        }
        return [...Array.from(this.filterParams.instructor.value), this.currentUser?.dependentId];
      default:
        if (!this.filterParams.instructor.value || this.filterParams.instructor.value.size === 0) {
          return [];
        }
        if (this.filterParams.instructor.value.size === this.filterParams.instructor.totalCount) {
          return [];
        }
        return Array.from(this.filterParams.instructor.value);
    }
  }

  getFilterParams(minDate: Date, maxDate: Date): ScheduleFilters {
    const minScheduleDateFilter = DateUtils.getUtcRangeForLocalDate(minDate).startUtc;
    const maxScheduleDateFilter = DateUtils.getUtcRangeForLocalDate(maxDate).endUtc;
    const instrumentIdFilter = Array.from(this.filterParams.instrument.value);
    this.setPlaceholder(this.filterParams.classType);

    if (instrumentIdFilter.length > 0 && this.filterParams.classType.value.has(ClassTypes.SUMMER_CAMP)) {
      instrumentIdFilter.push(0);
    }

    const persistentFilters = {
      ...this.appliedAdvanceFilter,
      selectedScheduleView: this.selectedScheduleView,
      locationIdFilter: Array.from(this.filterParams.location.value),
      instructorIdFilter: this.getInstructorFilter(),
      classTypeFilter: Array.from(this.filterParams.classType.value),
      instrumentIdFilter: instrumentIdFilter,
      maxScheduleDateFilter,
      minScheduleDateFilter
    };

     this.resources = this.currentUser?.userRoleId === this.constants.roleIds.INSTRUCTOR
      ? [{...this.currentUser, name: this.currentUser?.firstName + ' ' + this.currentUser?.lastName }]
      :  this.filterParams.instructor.value.size
      ? this.filterParams.instructor.options.filter((instructor: IdNameModel) => this.filterParams.instructor.value.has(instructor.id))
      : this.filterParams.instructor.options;

    return {
      ...persistentFilters,
      isNotShowDraftSchedule:
        this.currentUser?.userRoleId === this.constants.roleIds.INSTRUCTOR ||
        this.currentUser?.userRoleId === this.constants.roleIds.SUPERVISOR
          ? true
          : false
    };
  }

  getFiltersListData(): void {
    this.getLocations();
    this.getInstruments();
  }

  filterInstructors(showActiveStaffOnly: boolean): void {
    this.appliedAdvanceFilter.showActiveStaffOnly = showActiveStaffOnly;
    this.getInstructorsWithSupervisor(showActiveStaffOnly, false);
    this.initGetScheduleData(this.showScheduleForDate, this.showScheduleForDate);
  }

  getFilterParamsForInstructors(showActiveStaffOnly: boolean) {
    if (showActiveStaffOnly) {
      return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
        SupervisorIdFilter: this.currentUser?.isSupervisor ? this.currentUser?.dependentId : null,
        isSupervisorFilter: this.currentUser?.isSupervisor ? SupervisorFilter.SUPERVISOR_WITH_TEACHER : SupervisorFilter.ALL,
        Page: 1,
        locationIdFilter: Array.from(this.filterParams.location.value),
        scheduleStartDate: this.selectedScheduleView === this.schedulerViews.Week
          ? DateUtils.getUtcRangeForLocalDate(this.firstDateOfCurrentWeek).startUtc
          : DateUtils.getUtcRangeForLocalDate(this.showScheduleForDate).startUtc,
        scheduleEndDate: this.selectedScheduleView === this.schedulerViews.Week ? DateUtils.getUtcRangeForLocalDate(this.lastDateOfCurrentWeek).endUtc : DateUtils.getUtcRangeForLocalDate(this.showScheduleForDate).endUtc,
        daysOfSchedule: [moment(this.showScheduleForDate).day()]
      });
    }
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      SupervisorIdFilter: this.currentUser?.isSupervisor ? this.currentUser?.dependentId : null,
      isSupervisorFilter: this.currentUser?.isSupervisor ? SupervisorFilter.SUPERVISOR_WITH_TEACHER : SupervisorFilter.ALL,
      Page: 1
    });
  }

  getInstructorsWithSupervisor(showActiveStaffOnly: boolean, loadScheduleData = false): void {
    this.instructorService
      .add(this.getFilterParamsForInstructors(showActiveStaffOnly), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<InstructorList>) => {
          const items = res.result.items;
          const instructor = items.find(item => item.instructorDetail.id === this.currentUser?.dependentId) || {
            instructorDetail: { id: 0, name: '' }
          };
          this.filterParams.instructor.options =
            this.currentUser?.userRoleId === this.constants.roleIds.INSTRUCTOR
              ? [{ id: instructor.instructorDetail.id, name: instructor.instructorDetail.name }]
              : items.map(item => ({
                  ...item.instructorDetail
                }));

          this.filterParams.instructor.totalCount = items.length;

          if (loadScheduleData) {
            this.setLocationInDayView();
            const schedulerFilters = this.localStorageService.getItem(StorageItem.SchedulerFilters);
            this.filterParams.instructor.value = schedulerFilters?.instructorIdFilter
              ? new Set(schedulerFilters.instructorIdFilter)
              : new Set(this.filterParams.instructor.options.map(option => option.id));
          } 

          this.setPlaceholder(this.filterParams.instructor);
          this.resources = this.currentUser?.userRoleId === this.constants.roleIds.INSTRUCTOR
            ? [{...this.currentUser, name: this.currentUser?.firstName + ' ' + this.currentUser?.lastName }]
            : this.filterParams.instructor.value.size
            ? this.filterParams.instructor.options.filter((instructor: IdNameModel) =>
                this.filterParams.instructor.value.has(instructor.id)
              )
            : this.filterParams.instructor.options;

          if (loadScheduleData) {
            switch (this.selectedScheduleView) {
              case this.schedulerViews.Week:
                this.setFirstAndLastDateOfCurrentWeek(this.showScheduleForDate);
                break;
              default:
                this.initGetScheduleData(this.showScheduleForDate, this.showScheduleForDate);
                break;
            }
          }
          this.cdr.detectChanges();
        }
      });
  }

  getInstruments(): void {
    this.commonService
      .getInstruments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          const { items } = res.result;
          this.filterParams.instrument.options = items.map(item => ({
            id: item.instrumentDetail.id,
            name: item.instrumentDetail.name
          }));
          this.filterParams.instrument.value = new Set(items.map(item => item.instrumentDetail.id));
          this.filterParams.instrument.totalCount = items.length;
          const schedulerFilters = this.localStorageService.getItem(StorageItem.SchedulerFilters);
          this.filterParams.instrument.value = new Set(schedulerFilters?.instrumentIdFilter || this.filterParams.instrument.value);
          this.setPlaceholder(this.filterParams.instrument);
          this.cdr.detectChanges();
        }
      });
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          const { items } = res.result;
          this.filterParams.location.options = items.map(item => ({
            id: item.schoolLocations.id,
            name: item.schoolLocations.locationName
          }));
          this.filterParams.location.value = new Set(items.map(item => item.schoolLocations.id));
          this.filterParams.location.totalCount = items.length;
          const schedulerFilters = this.localStorageService.getItem(StorageItem.SchedulerFilters);
          this.filterParams.location.value = new Set(schedulerFilters?.locationIdFilter || this.filterParams.location.value);
          this.setPlaceholder(this.filterParams.location);
          this.getInstructorsWithSupervisor(
            this.localStorageService.getItem(StorageItem.SchedulerFilters)?.showActiveStaffOnly ||
              this.appliedAdvanceFilter.showActiveStaffOnly,
            true
          );
          this.cdr.detectChanges();
        }
      });
  }

  getCurrentUser(): void {
    this.isLoading = true;
    this.currentUser = this.localStorageService.getItem(StorageItem.CurrentUser);
  }

  applyAdvanceFilters(advanceFilters: AdvancedFilters): void {
    this.appliedAdvanceFilter = advanceFilters;
    this.setDateToCurrentDate(false);
    this.isAdvanceFilterOpen = false;
  }

  openAdvanceFilterAndStoreInitialFilters(): void {
    this.originalAdvanceFIlters = JSON.parse(JSON.stringify(this.appliedAdvanceFilter));
    this.isAdvanceFilterOpen = true;
  }

  closeAdvanceFilterAndRestoreData(): void {
    this.appliedAdvanceFilter = this.originalAdvanceFIlters;
    this.isAdvanceFilterOpen = false;
  }

  openScheduleUpdateModel(event: ScheduleDetailsView): void {
    this.selectedEvent = event;
    this.isUpdateSchedule = true;
  }

  setPlaceholder(filter: FilterItem): void {
    const selectedValues = Array.from(filter.value);
    const firstSelectedValueName = filter.options.find(option => option.id === selectedValues[0])?.name;
    const additionalCount = selectedValues.length > 1 ? `+${selectedValues.length - 1}` : '';
    if (!selectedValues.length || selectedValues.length === filter.totalCount) {
      filter.placeholder = filter.defaultPlaceholder;
      return;
    }
    filter.placeholder = ` ${firstSelectedValueName} ${additionalCount}`;
  }

  removeSelectedLocation(): void {
    const selectedValues = Array.from(this.filterParams.location.value);
    const firstSelectedValueName = this.filterParams.location.options.find(option => option.id === selectedValues[0])?.name;
    const additionalCount = selectedValues.length > 1 ? `+${selectedValues.length - 1}` : '';
    this.filterParams.location.placeholder = ` ${firstSelectedValueName} ${additionalCount}`;
    this.setDateToCurrentDate(false);
  }

  goToPreviousDate(): void {
    const previousDate = new Date(this.showScheduleForDate);
    switch (this.selectedScheduleView) {
      case this.schedulerViews.Week:
        previousDate.setDate(this.showScheduleForDate.getDate() - 7);
        this.showScheduleForDate = previousDate;
        this.setFirstAndLastDateOfCurrentWeek(previousDate);
        break;
      default:
        previousDate.setDate(this.showScheduleForDate.getDate() - 1);
        this.showScheduleForDate = previousDate;
        this.getInstructorsWithSupervisor(this.appliedAdvanceFilter.showActiveStaffOnly, false);
        this.initGetScheduleData(previousDate, previousDate);
        break;
    }
    this.scheduleDate.emit(this.showScheduleForDate);
  }

  goToNextDate(): void {
    this.isLoading = true;
    const nextDate = new Date(this.showScheduleForDate);
    switch (this.selectedScheduleView) {
      case this.schedulerViews.Week:
        nextDate.setDate(this.showScheduleForDate.getDate() + 7);
        this.showScheduleForDate = nextDate;
        this.setFirstAndLastDateOfCurrentWeek(nextDate);
        break;
      default:
        nextDate.setDate(this.showScheduleForDate.getDate() + 1);
        this.showScheduleForDate = nextDate;
        this.getInstructorsWithSupervisor(this.appliedAdvanceFilter.showActiveStaffOnly, false);
        this.initGetScheduleData(nextDate, nextDate);
        break;
    }
    this.scheduleDate.emit(this.showScheduleForDate);
  }

  setLocationInDayView(): void {
    if (this.selectedScheduleView === this.schedulerViews.Day) {
      if (this.filterParams.location.options.length >= 2) {
        const firstSelectedValueName = this.filterParams.location.options[0].name;
        this.filterParams.location.placeholder = ` ${firstSelectedValueName} +1`;
        this.filterParams.location.value = new Set([this.filterParams.location.options[0].id, this.filterParams.location.options[1].id]);
      }
      return;
    }
  }

  setDateToCurrentDate(currentDate = true): void {
    this.showScheduleForDate = currentDate ? new Date() : this.showScheduleForDate;
    this.scheduleDate.emit(this.showScheduleForDate);
    this.getInstructorsWithSupervisor(this.appliedAdvanceFilter.showActiveStaffOnly, false);

    switch (this.selectedScheduleView) {
      case this.schedulerViews.Week:
        this.setFirstAndLastDateOfCurrentWeek(this.showScheduleForDate);
        break;
      default:
        this.initGetScheduleData(this.showScheduleForDate, this.showScheduleForDate);
        break;
    }
  }

  onCalendarDateChange(date: Date): void {
    this.showScheduleForDate = date;
    this.setDateToCurrentDate(false);
    this.scheduleDate.emit(this.showScheduleForDate);
  }

  setFirstAndLastDateOfCurrentWeek(currentDate: Date): void {
    this.firstDateOfCurrentWeek = this.getFirstDateOfCurrentWeek(currentDate);
    this.lastDateOfCurrentWeek = new Date(this.firstDateOfCurrentWeek);
    this.lastDateOfCurrentWeek.setDate(this.firstDateOfCurrentWeek.getDate() + 6);
    this.initGetScheduleData(this.firstDateOfCurrentWeek, this.lastDateOfCurrentWeek);
  }

  getFirstDateOfCurrentWeek(currentDate: Date): Date {
    const date = new Date(currentDate);
    const dayOfWeek = date.getDay();
    const diff = date.getDate() - dayOfWeek;
    date.setDate(diff);
    date.setHours(0, 0, 0, 0);
    return date;
  }

  asIsOrder() {
    return 1;
  }
}