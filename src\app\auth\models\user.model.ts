import { Form<PERSON>rray, FormControl, FormGroup } from '@angular/forms';
import { DependentInfo, DependentInfoForm } from './sign-up.model';
import {
  InstructorInstrumentFormGroup,
  InstructorAvailabilityFormGroup,
  InstructorAvaibilityInInstructorDetail,
  LeaveBalanceFormGroup
} from 'src/app/pages/members/pages/instructors/models';
import { InstructorInstrument } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';

export interface UserFormGroupType {
  firstName: FormControl<string>;
  lastName: FormControl<string>;
  emailAddress: FormControl<string>;
  phoneNumber: FormControl<string>;
  address: FormControl<string>;
  stateId: FormControl<number | undefined>;
  city: FormControl<string>;
  zipCode: FormControl<string>;
  dateOfBirth: FormControl<string>;
  dependentDetails: FormArray<FormGroup<DependentInfoForm>>;
  profilePicture: FormControl<string | null>;
  profilePhoto: FormControl<string | null>;
  profilePic: FormControl<string | null>;
  profilePicturefullurl: FormControl<string | null>;
  bio: FormControl<string>;
  instructorInstrument: FormArray<FormGroup<InstructorInstrumentFormGroup>>;
  instructorAvailability: FormArray<FormGroup<InstructorAvailabilityFormGroup>>;
  deskManagerAvailabilityAndLocations: FormArray<FormGroup<InstructorAvailabilityFormGroup>>;
  leaveBalances: FormArray<FormGroup<LeaveBalanceFormGroup>>;
  isNoSubstitute: FormControl<boolean>;
}

export interface AddressForm {
  firstName: FormControl<string>;
  lastName: FormControl<string>;
  address: FormControl<string>;
  city: FormControl<string>;
  state: FormControl<string>;
  zipCode: FormControl<string>;
}

export interface Address {
  firstName: string;
  lastName: string;
  address: string;
  city: string;
  state: string;
  stateId?: number;
  zipCode: string;
  saveAddress?: boolean;
}

export interface Account extends Address {
  dateOfBirth: string;
  dependentDetails: Array<DependentInfo>;
  allAttendedAtSameLocation: boolean;
  emailAddress: string;
  isDependent: boolean;
  isGoogleAuthenticatorEnabled: boolean;
  isPhoneNumberConfirmed: boolean;
  locationId: number;
  phoneNumber: string | undefined;
  profilePicture: string;
  profilePicturefullurl: string;
  qrCodeSetupImageUrl: string;
  referenceBy: string;
  referenceFrom: string;
  referralCode: string;
  timezone: string;
  userName: string;
  userRole: string;
  userId: number;
  dependentId: number;
  userRoleId: number;
  bio: string;
  instruments: Array<InstructorInstrument>;
  instructorAvailability: Array<InstructorAvaibilityInInstructorDetail>;
  isSupervisor: boolean;
  userType: number;
  deskManagerAvailabilityAndLocations: Array<InstructorAvaibilityInInstructorDetail>;
  pendingDocumentCount: number;
  summerCampDiscountPercentage: number;
  productDiscountPercentage: number;
  isNoSubstitute: boolean;
}

export interface State {
  id: number;
  name: string;
  abbreviation: string;
}
