import { FormControl } from '@angular/forms';
import { InstructorInfo } from 'src/app/pages/schedule-classes/pages/group-class/models';
import { DependentDetails, FilterItem } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { InstrumentsDetail } from 'src/app/request-information/models';

export interface Students {
  dependentInformation: DependentInformations;
}

export interface DependentInformations {
  id: number;
  firstName: string;
  lastName: string;
  age: number;
  locationId: number;
  userType: number;
  mentorId: number;
  instructorsDetails: Array<InstructorInfo>;
  instruments: Array<InstrumentsDetail>;
  skill: string;
  accountManagerEmail: string;
  accountManagerId: number;
  accountManagerName: string;
  accountManagerPhoneNo: string;
  isAccountManager: boolean;
  accountManagerUserType: number;
  accountManagerDependentsInfo: Array<DependentDetails>;
  accountManagerDependentId: number;
  accountManagerDateOfBirth: string;
  accountManagerAddress: string;
  accountManagerLastName: string;
  accountManagerFirstName: string;
  accountManagerLocationId: number;
  isNoSubstitute: boolean;
}

export interface StudentFilters {
  searchTerm: string | null;
  instructorId: FilterItem;
  instrumentId: FilterItem;
  age: number;
  skillId: number;
}

export interface DependentInformationParams {
  startDate: string;
  endDate: string;
  dependentId: number | undefined;
}

export interface StudentGrades {
  studentGrade: StudentGradeInfo;
}

export interface StudentGradeInfo {
  grade: number;
  studentId: number;
  instrumentId: number | undefined;
  instructorId: number;
  studentName: string;
  instructorName: string;
  instrumentName: string;
  id: number | undefined;
  updatedOn: string;
}

export interface StudentGradeFormGroup {
  id: FormControl<number | undefined>;
  studentId: FormControl<number | undefined>;
  instrumentId: FormControl<number | undefined>;
  grade: FormControl<number | undefined>;
}

export interface MakeUpPassParams {
  scheduleId: number;
  studentId: number;
  instrumentId: number;
}

export interface AssignProductFilter {
  searchTerm: string | null;
}

export interface IdEmailModel {
  id?: number;
  email?: string;
}

export interface StudentNotes {
  id: number;
  dependentInformationId: number;
  notes: string;
  updatedUserId: number;
  updatedUserName: string;
  updatedTime: string;
  profilePictureUrl: string;
}

export interface StudentAttendance {
  studentAllAttendance: StudentAllAttendance;
}

export interface StudentAllAttendance {
  id: number;
  classType: number;
  scheduleDate: string;
  startTime: string;
  endTime: string;
  locationId: number;
  locationName: string;
  instrumentId: number;
  instrumentName: string;
  isPresent: boolean;
  isCancelled: boolean;
  ensembleClassName: string;
  campName: string;
  groupClassName: string;
}

export enum AttendanceType {
  ALL = 1,
  COMPLETE = 2,
  NO_SHOW = 3,
  INCOMPLETE = 4
}

export enum AccordionType {
  ASSIGNED_PLAN = 1,
  ENSEMBLE_PLAN = 2,
  OTHER_PLAN = 3,
  ASSIGNED_PRODUCT = 4,
  RENTAL_PLAN = 5
}

export enum AssignItemType {
  PLANS = 1,
  PRODUCTS = 2
}