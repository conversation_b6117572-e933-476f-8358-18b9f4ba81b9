<mat-sidenav-container class="example-container h-100" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="sidebar-w-850"
    [disableClose]="true">
    @if (isSideNavOpen) {
      <app-quit-plan-request
        (closeSideNav)="toggleSideNav(false, null)"
        [selectedPlanDetail]="selectedPlanDetail"
        (refreshData)="getPlanAndPassesDetail()"></app-quit-plan-request>
    }
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="header-tab-with-btn">
      <div class="tab-item-content">
        @for (pageTabOption of pageTabOptions | keyvalue: keepOriginalOrder; track $index) {
          <div
            [ngClass]="{ item: true, 'active-item': selectedTabOption === pageTabOption.value }"
            (click)="setActiveTabOption(pageTabOption.value)">
            {{ pageTabOption.value }}
          </div>
        }
      </div>
    </div>

    <div class="auth-page-with-header">
      <div class="search-and-count-wrapper-auth">
        <div class="search-and-count-wrapper">
          <div class="total-users">
            Total:
            <span>{{
              selectedTabOption === pageTabOptions.PLAN
                ? filteredPlans && filteredPlans.length
                : filteredPasses && filteredPasses.length
            }}</span>
          </div>
        </div>
        <ng-container
          [ngTemplateOutlet]="selectedTabOption === pageTabOptions.PLAN ? planFilters : passFilters"></ng-container>
      </div>
      <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : planAndPassesLists"></ng-container>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #planFilters>
  <div class="filter-wrapper">
    <mat-form-field class="search-bar-wrapper">
      <mat-select [(ngModel)]="filters.planActiveFilter" (selectionChange)="applyPlanFilters()">
        <mat-option [value]="all.ALL">All Plan</mat-option>
        <mat-option *ngFor="let activePlan of activePlans | enumToKeyValue" [value]="activePlan.value">
          {{ activePlan.key | titlecase }} Plans
        </mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field class="search-bar-wrapper">
      <mat-select [(ngModel)]="filters.durationFilter" (selectionChange)="applyPlanFilters()">
        <mat-option [value]="all.ALL">All Duration</mat-option>
        <mat-option *ngFor="let duration of durations | enumToKeyValue" [value]="duration.value">
          {{ duration.value }} Minutes
        </mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field class="search-bar-wrapper">
      <mat-select [(ngModel)]="filters.visitsPerWeekFilter" (selectionChange)="applyPlanFilters()">
        <mat-option [value]="all.ALL">All Visits per week</mat-option>
        <mat-option *ngFor="let visit of visits | enumToKeyValue" [value]="visit.value">
          {{ visit.value }} Visit
        </mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field class="search-bar-wrapper">
      <mat-select [(ngModel)]="filters.planTypeFilter" (selectionChange)="applyPlanFilters()">
        <mat-option [value]="all.ALL">All Plan Type</mat-option>
        <mat-option *ngFor="let planType of planTypes | enumToKeyValue" [value]="planType.value">
          {{ planType.key | titlecase }}
        </mat-option>
      </mat-select>
    </mat-form-field>
    <!-- <mat-form-field class="search-bar-wrapper">
      <mat-select [(ngModel)]="filters.planFilter" (selectionChange)="applyPlanFilters()">
        <mat-option [value]="all.ALL">All Plan</mat-option>
        <mat-option *ngFor="let plan of plans | enumToKeyValue" [value]="plan.value">
          {{ plan.key | titlecase }}
        </mat-option>
      </mat-select>
    </mat-form-field> -->
  </div>
</ng-template>

<ng-template #passFilters>
  <div class="filter-wrapper">
    <mat-form-field class="search-bar-wrapper">
      <mat-select [(ngModel)]="filters.passStatusFilter" (selectionChange)="applyPassFilters()">
        <mat-option *ngFor="let passStatus of passStatuses | enumToKeyValue" [value]="passStatus.value">
          {{ passStatus.key | titlecase }} Pass
        </mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field class="search-bar-wrapper">
      <mat-select [(ngModel)]="filters.durationFilter" (selectionChange)="applyPassFilters()">
        <mat-option [value]="all.ALL">All Duration</mat-option>
        <mat-option *ngFor="let duration of durations | enumToKeyValue" [value]="duration.value">
          {{ duration.value }} Minutes
        </mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field class="search-bar-wrapper">
      <mat-select [(ngModel)]="filters.visitsPerWeekFilter" (selectionChange)="applyPassFilters()">
        <mat-option [value]="all.ALL">All Visits per week</mat-option>
        <mat-option *ngFor="let visit of visits | enumToKeyValue" [value]="visit.value">
          {{ visit.value }} Visit
        </mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field class="search-bar-wrapper">
      <mat-select [(ngModel)]="filters.passTypeFilter" (selectionChange)="applyPassFilters()">
        <mat-option [value]="all.ALL">All Pass Type</mat-option>
        <mat-option *ngFor="let passType of passTypes | enumToKeyValue" [value]="passType.value">
          {{ passType.key.replace("_", " ") | titlecase }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>
</ng-template>

<ng-template #planAndPassesLists>
  <ng-container
    [ngTemplateOutlet]="
      (
        selectedTabOption === pageTabOptions.PLAN
          ? filteredPlans && filteredPlans.length
          : filteredPasses && filteredPasses.length
      )
        ? planAndPassesList
        : noDataFound
    "></ng-container>
</ng-template>

<ng-template #planAndPassesList>
  <div class="plan-and-passes-list">
    <ng-container [ngTemplateOutlet]="selectedTabOption === pageTabOptions.PLAN ? planList : passList"></ng-container>
  </div>
</ng-template>

<ng-template #planList>
  @for (plan of filteredPlans; track $index) {
    <div class="o-card mb-2">
      <div class="o-card-body">
        <div class="d-flex justify-content-between">
          <div>
            <div class="title">
              @if (plan.isRentalPlan) {  
                {{ plan.instrumentName }} Rental Plan {{ plan.isPlanWithInsurance ? 'With Insurance' : 'Without Insurance' }}
              }
              @else {
                Weekly {{ plan.isEnsembleAvailable ? "Ensemble" : plan.instrumentName }} Lessons ({{ planSummaryService.getPlanType(plan.planType) }}-{{
                  planSummaryService.getPlanSummary(plan.planDetails)
                }})
              }  
            </div>
            <div class="plan-pass-content">
              @if (!plan.isRentalPlan) { 
                <div class="primary-color">{{ planSummaryService.getTotalVisits(plan.planDetails) }} visits per week</div>
                <div class="dot"></div>
              }
              <div>{{ plan.plan === plans.RECURRING ? "Recurring" : "Custom" }} plan</div>
              <div class="dot"></div>
              <div class="dependent-name">
                Client <span>{{ plan.dependentName }}</span>
              </div>
              @if (
                plan.studentDiscontinuedPlanDetails?.planCancelRequestStatus === planStatuses.PENDING ||
                plan.studentDiscontinuedPlanDetails?.planCancelRequestStatus === planStatuses.APPROVED
              ) {
                <div class="dot"></div>
                <div class="fw-bold">
                  Request on
                  <span class="text-gray">{{ plan.studentDiscontinuedPlanDetails?.requestDate | localDate | date }}</span>
                </div>
              }

              @if (plan?.assignedPlanStatus) {
                <div class="dot"></div>
                <div class="fw-bold">
                  Status
                  <span class="text-gray">
                    @switch (plan?.assignedPlanStatus) {
                      @case (planProgressStatus.PAYMENT_DONE) {
                        Payment Done
                      }
                      @case (planProgressStatus.SCHEDULE_CREATED) {
                        Scheduled Created
                      }
                      @case (planProgressStatus.CANCELED) {
                        Canceled
                      }
                      @default {
                        Assigned
                      }
                    }
                  </span>
                </div>
              }
            </div>
          </div>
          <div class="schedule-btn">
            @if (plan.studentDiscontinuedPlanDetails) {
              @switch (plan.studentDiscontinuedPlanDetails.planCancelRequestStatus) {
                @case (planStatuses.PENDING) {
                  <button
                    mat-raised-button
                    color="primary"
                    class="mat-primary-btn action-btn"
                    type="button"
                    (click)="openCancelRequestConfirmation(plan.studentDiscontinuedPlanDetails.planCancelRequestId)">
                    Cancel Request
                  </button>
                }
                @case (planStatuses.APPROVED) {
                  <div class="status">Discontinued on {{ plan.studentDiscontinuedPlanDetails.approvedDate | localDate | date }}</div>
                }
                @default {
                  <button
                    mat-raised-button
                    color="primary"
                    class="mat-red-btn action-btn"
                    type="button"
                    (click)="toggleSideNav(true, plan)">
                    Quit
                  </button>
                }
              }
            }
            @else {
              @switch (plan.assignedPlanStatus) {
                @case (planProgressStatus.CANCELED) {
                  <div class="status">Discontinued on {{ plan.canceledOn | localDate | date }}</div>
                }
                @default {
                  <button
                    mat-raised-button
                    color="primary"
                    class="mat-red-btn action-btn"
                    type="button"
                    (click)="toggleSideNav(true, plan)">
                    Quit
                  </button>
                }
              }
            }
          </div>
        </div>
        <div>
          @if (
            plan.studentDiscontinuedPlanDetails?.planCancelRequestStatus === planStatuses.PENDING ||
            plan.studentDiscontinuedPlanDetails?.planCancelRequestStatus === planStatuses.APPROVED ||
            plan.assignedPlanStatus === planProgressStatus.CANCELED
          ) {
            <div class="dotted-divider"></div>
            <div class="reason">
              Reason <span>{{ plan.studentDiscontinuedPlanDetails?.reason ?? plan.canceledReason }}</span>
            </div>
          }
          @if (
            plan.studentDiscontinuedPlanDetails?.planCancelRequestStatus === planStatuses.CANCELED &&
            plan.studentDiscontinuedPlanDetails?.approveRemark
          ) {
            <div class="dotted-divider"></div>
            <div class="reason">
              Request Canceled Reason <span>{{ plan.studentDiscontinuedPlanDetails?.approveRemark }}</span>
            </div>
          }
        </div>
      </div>
    </div>
  }
</ng-template>

<ng-template #passList>
  @for (pass of filteredPasses; track $index) {
    <div class="o-card mb-2">
      <div class="o-card-body d-flex justify-content-between">
        <div>
          <div class="title">
            <span *ngIf="pass.isUsed">Used: </span>
            <span *ngIf="checkIfPassIsExpired(pass.expiryDate) && !pass.isUsed">Expired: </span> {{ pass.passName }} ({{
              pass.duration
            }})
          </div>
          <div class="pass-info">For use in In-Person {{ pass.passName }} ({{ pass.duration }})</div>
          <div class="plan-pass-content">
            <div class="primary-color">{{ pass.visits }} visit</div>
            <div class="dot"></div>
            <div class="dependent-name">
              Student <span>{{ pass.dependentName }}</span>
            </div>
          </div>
        </div>
        <div class="schedule-btn" *ngIf="!pass.isUsed && !checkIfPassIsExpired(pass.expiryDate)">
          <button
            mat-raised-button
            color="primary"
            class="mat-primary-btn action-btn"
            type="button"
            (click)="navigateToMakeUpLesson(pass)">
            Schedule
          </button>
        </div>
      </div>
    </div>
  }
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-card">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <app-content-loader></app-content-loader>
</ng-template>
