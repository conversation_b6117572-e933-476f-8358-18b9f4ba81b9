import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSidenavModule } from '@angular/material/sidenav';
import { NgxPaginationModule } from 'ngx-pagination';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import {
  InstructorAvaibilityInInstructorDetail,
  InstructorDetails,
  InstructorFilters,
  Instructors,
  LocationDetailInInstructor
} from './models';
import { Debounce } from 'src/app/shared/decorators';
import { CBGetResponse, CBResponse, IdNameModel } from 'src/app/shared/models';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { InstructorService } from 'src/app/schedule-introductory-lesson/services';
import { CommonUtils } from 'src/app/shared/utils';
import { FiltersEnum, SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { MatSelectModule } from '@angular/material/select';
import { AuthService } from 'src/app/auth/services';
import { InstrumentsService } from 'src/app/request-information/services';
import { Instrument } from 'src/app/request-information/models';
import { AddInstructorComponent } from './pages/add-instructor/add-instructor.component';
import { ViewInstructorComponent } from './pages/view-instructor/view-instructor.component';
import { SupervisorFilter } from '../supervisors/models';
import { MatTooltipModule } from '@angular/material/tooltip';
import { InstructorInstrument } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { MultiSelectComponent } from 'src/app/shared/components/multi-select/multi-select.component';
import { CommonService, NavigationService } from 'src/app/shared/services';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { ActivatedRoute } from '@angular/router';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    MatIconModule,
    MatInputModule,
    MatSidenavModule,
    NgxPaginationModule,
    CommonModule,
    SharedModule,
    MatSelectModule,
    MatTooltipModule
  ],
  COMPONENTS: [AddInstructorComponent, ViewInstructorComponent, MultiSelectComponent]
};

@Component({
  selector: 'app-instructors',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './instructors.component.html',
  styleUrl: './instructors.component.scss'
})
export class InstructorsComponent extends BaseComponent implements OnInit {
  totalCount!: number;
  instructors!: Array<Instructors>;
  instrumentTypes!: Array<Instrument>;
  schoolLocations!: Array<SchoolLocations>;

  AllLocation = FiltersEnum;
  pageSize = this.paginationConfig.itemsPerPage;
  currentPage = this.paginationConfig.pageNumber;
  isAddInstructorSideNavOpen = false;
  isAddInstructor = true;
  selectedInstructorViewDetails!: InstructorDetails | null;

  filters: InstructorFilters = {
    searchTerm: null,
    locationId: {
      id: 1,
      defaultPlaceholder: 'All Locations',
      placeholder: 'All Locations',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    instrumentId: {
      id: 2,
      defaultPlaceholder: 'All Instruments',
      placeholder: 'All Instruments',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    }
  };

  constructor(
    private readonly instructorService: InstructorService,
    private readonly cdr: ChangeDetectorRef,
    private readonly authService: AuthService,
    private readonly commonService: CommonService,
    private readonly instrumentsService: InstrumentsService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly navigationService: NavigationService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getInstructorIdFromQueryParams();
    this.getCurrentUser();
    this.getAllLocations();
    this.getInstruments();
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.getInstructors(this.currentPage, this.pageSize);
  }

  getInstructorIdFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params.instructorId) {
        this.getInstructorDetails(+params.instructorId);
      }
      this.cdr.detectChanges();
    });
  }

  @Debounce(500)
  getInstructors(currentPage: number, pageSize: number): void {
    this.showPageLoader = true;
    this.cdr.detectChanges();

    this.instructorService
      .add(this.getFilterParams(currentPage, pageSize), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instructors>) => {
          this.totalCount = res.result.totalCount;
          this.instructors = res.result.items;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getCurrentUser(): void {
    this.showPageLoader = true;
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.getInstructors(this.currentPage, this.pageSize);
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getFilterParams(currentPage: number, pageSize: number) {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      NameFilter: this.filters.searchTerm,
      LocationIdFilter: [...this.filters.locationId.value],
      InstrumentIdFilter: [...this.filters.instrumentId.value],
      SupervisorIdFilter: this.currentUser?.isSupervisor ? this.currentUser?.dependentId : undefined,
      IsSupervisorFilter: this.currentUser?.isSupervisor ? SupervisorFilter.SUPERVISOR : SupervisorFilter.TEACHER,
      page: currentPage,
      pageSize: pageSize
    });
  }

  getInstructorDetails(instructorId: number): void {
    this.instructorService
      .getList<CBGetResponse<Instructors>>(`${API_URL.instructorDetails.getInstructorDetailForView}?id=${instructorId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<Instructors>) => {
          this.selectedInstructorViewDetails = res.result.instructorDetail;
          this.openAddOrViewInstructor(false, this.selectedInstructorViewDetails);
          this.cdr.detectChanges();
        }
      });
  }

  getLocationDetails(array: InstructorAvaibilityInInstructorDetail[]): LocationDetailInInstructor {
    const uniqueLocations = new Set(array.map(item => item.locationName));
    return {
      count: uniqueLocations.size,
      names: Array.from(uniqueLocations).slice(1).join(', ')
    };
  }

  getInstrumentNames(array: InstructorInstrument[]): string {
    return array
      .slice(1)
      .map(item => item.name)
      .join(', ');
  }

  openAddOrViewInstructor(isAdd: boolean, instructor: InstructorDetails | null): void {
    this.isAddInstructor = isAdd;
    this.isAddInstructorSideNavOpen = true;
    this.selectedInstructorViewDetails = instructor;
  }

  openInstructorDetails(instructorId: number): void {
    this.navigationService.navigateToInstructorDetail(instructorId);
  }

  closeAddOrEditInstructor(instructor: InstructorDetails | null): void {
    this.isAddInstructor = false;
    this.isAddInstructorSideNavOpen = false;
    this.selectedInstructorViewDetails = instructor;
  }

  getAllLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.schoolLocations = res.result.items;
          this.filters.locationId.options = res.result.items.map(location => ({
            id: location.schoolLocations.id,
            name: location.schoolLocations.locationName
          }));
          this.filters.locationId.totalCount = this.schoolLocations.length;
          this.cdr.detectChanges();
        }
      });
  }

  getInstruments(): void {
    this.instrumentsService
      .getList<CBResponse<Instrument>>(API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instrumentTypes = res.result.items;
          this.filters.instrumentId.options = res.result.items.map(instrument => ({
            id: instrument.instrumentDetail.id,
            name: instrument.instrumentDetail.name
          }));
          this.filters.instrumentId.totalCount = this.instrumentTypes.length;
          this.cdr.detectChanges();
        }
      });
  }

  getInitials(name: string): string {
    return CommonUtils.getInitialsUsingFullName(name);
  }

  @Debounce(300)
  onSearchTermChanged(): void {
    this.currentPage = 1;
    this.getInstructors(this.currentPage, this.pageSize);
  }
}
