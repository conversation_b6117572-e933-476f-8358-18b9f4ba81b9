<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">
      {{ isRePayment ? 'Retry Payment' : isScheduleMakeUpLesson || isPaymentDone ? 'Book Your Lesson' : 'Enrolment and payment' }}
    </div>
    <div class="action-btn-wrapper">
      <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button" (click)="onCloseModal()">Close</button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : enrollLesson"></ng-container>
  </div>
</div>

<ng-template #enrollLesson>
  <ng-container [ngTemplateOutlet]="isScheduleMakeUpLesson || isPaymentDone ? makeUpLessonDetail : scheduleClassDetail"></ng-container>
</ng-template>

<ng-template #makeUpLessonDetail>
  <div class="lesson-cancel-wrapper">
    <div>
      <img [src]="constants.staticImages.icons.checkCircle" height="80" width="80" alt="" />
      <div class="success-title">
        <ng-container [ngTemplateOutlet]="classNameTemp"></ng-container>
      </div>
      <div class="success-message">
        <div>
          <span>{{ getDependentNameFromValue(scheduleInfo.studentId) | titlecase }}</span> for
          <span>
            <ng-container [ngTemplateOutlet]="classNameTemp"></ng-container>
          </span>
        </div>
        <div>
          with
          <span>
            @if(scheduleInfo.classType === classTypes.ENSEMBLE_CLASS) {
            {{ scheduleInfo.assignedInstructors![0].instructorName }}
            @if(scheduleInfo.assignedInstructors!.length > 1) {
            <div class="dot d-inline-block"></div>
            <span [matTooltip]="getInstructorNames(scheduleInfo.assignedInstructors!)"
              >+{{ scheduleInfo.assignedInstructors!.length - 1 }}</span
            >
            } } @else {
            {{ scheduleInfo.instructorName }}
            }
          </span>
          at
        </div>
        <div>
          <span>{{ getLocationNameFromValue(scheduleInfo.locationId) }}</span> on
          <span
            >{{ scheduleInfo.scheduleStartTime | date : constants.dateFormats.MMM_d }},
            {{ scheduleInfo.scheduleStartTime | date : 'shortTime' }} - {{ scheduleInfo.scheduleEndTime | date : 'shortTime' }}</span
          >
        </div>
        <div *ngIf="scheduleInfo.roomName">
          Room <span>{{ scheduleInfo.roomName }}</span>
        </div>
      </div>
      <div class="action-btn mt-4">
        @if (!isPaymentDone) {
        <button mat-raised-button color="accent" class="mat-accent-btn back-btn me-2" type="button" (click)="onCloseModal()">
          Make Changes
        </button>
        <button
          mat-raised-button
          color="primary"
          class="mat-primary-btn"
          type="button"
          (click)="onScheduleMakeUpClass()"
          [appLoader]="showBtnLoader"
        >
          Ok, Done
        </button>
        }
      </div>
    </div>
  </div>
</ng-template>

<ng-template #scheduleClassDetail>
  <div class="group-class-detail-wrapper">
    <div class="schedule-information-title">
      <div>Schedule Information</div>
    </div>
    <div class="schedule-basic-details">
      <div class="schedule-info-header">
        <div class="group-name-age">
          <ng-container [ngTemplateOutlet]="classNameTemp"></ng-container>
        </div>
      </div>

      <div class="schedule-info">
        <img [src]="constants.staticImages.icons.location" alt="" />
        <div class="schedule-info-content">
          {{ getLocationNameFromValue(scheduleInfo.locationId) }}
        </div>
      </div>
      <div class="schedule-info" *ngIf="scheduleInfo.classType !== classTypes.SUMMER_CAMP">
        <img [src]="constants.staticImages.icons.instrumentIcon" alt="" />
        <div class="schedule-info-content d-flex align-items-center">
          @if(scheduleInfo.classType === classTypes.ENSEMBLE_CLASS) {
          {{ scheduleInfo.assignedInstruments![0].instrumentName }}
          @if(scheduleInfo.assignedInstruments!.length > 1) {
          <div class="dot d-inline-block"></div>
          <span [matTooltip]="getInstrumentNames(scheduleInfo.assignedInstruments!)"
            >+{{ scheduleInfo.assignedInstruments!.length - 1 }}</span
          >
          } } @else {
          {{ scheduleInfo.instrumentName }}
          }
        </div>
      </div>
      <div class="schedule-info">
        <img [src]="constants.staticImages.icons.profileCircle" alt="" />
        <div class="schedule-info-content d-flex align-items-center">
          <span class="gray-text d-flex align-items-center">With</span>
          @if(scheduleInfo.classType === classTypes.ENSEMBLE_CLASS) {
          {{ scheduleInfo.assignedInstructors![0].instructorName }}
          @if(scheduleInfo.assignedInstructors!.length > 1) {
          <div class="dot d-inline-block"></div>
          <span [matTooltip]="getInstructorNames(scheduleInfo.assignedInstructors!)"
            >+{{ scheduleInfo.assignedInstructors!.length - 1 }}</span
          >
          } } @else {
          {{ scheduleInfo.instructorName }}
          }
          <span class="gray-text ms-1 me-1">For</span>
          {{ getDependentNameFromValue(scheduleInfo.studentId) | titlecase }}
        </div>
      </div>
      <div class="schedule-info">
        <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
        <div class="schedule-info-content">
          {{ scheduleInfo.scheduleStartTime | localDate | date : 'mediumDate' }}
          <span class="gray-text" *ngIf="scheduleInfo.classType !== classTypes.INTRODUCTORY"
            >(Every
            {{ !scheduleInfo.instrumentId ? 'Day' : (scheduleInfo.scheduleStartTime | localDate | date : constants.dateFormats.fullDay) }}
            For
            <span class="primary-color"
              >{{ schedulerService.getNumberOfWeeks(scheduleInfo.startDate!.toString() | localDate, scheduleInfo.endDate!.toString() | localDate) }} Weeks</span
            >)
          </span>
        </div>
      </div>
      @if (classTypes.GROUP_CLASS === scheduleInfo.classType || classTypes.ENSEMBLE_CLASS === scheduleInfo.classType) {
      <div class="schedule-info">
        <img [src]="constants.staticImages.icons.people" alt="" />
        <div class="schedule-info-content">
          Client Capacity
          <span class="primary-color">{{ scheduleInfo.enrolledStudents }}/{{ scheduleInfo.studentCapacity }}</span>
        </div>
      </div>
      }
      <div class="schedule-info">
        <img [src]="constants.staticImages.icons.timeCircleClock" alt="" />
        @if (classTypes.INTRODUCTORY === scheduleInfo.classType) {
        <div class="schedule-info-content">30 Min</div>
        } @else {
        <div class="schedule-info-content">
          {{ scheduleInfo.scheduleStartTime | localDate | date : 'shortTime' }} -
          {{ scheduleInfo.scheduleEndTime | localDate | date : 'shortTime' }}
          <span *ngIf="scheduleInfo.instrumentId" class="primary-color"> ({{ scheduleInfo.duration }} Min)</span>
        </div>
        }
      </div>
      <div class="payment-info">
        <div class="dotted-divider"></div>
        <div class="schedule-info-content">
          <div class="payment-details-wrapper">
            <div class="title">Payment Details</div>
            <div class="payment-details-content">
              <div class="space-between mb-4">
                <div class="sub-title">
                  <ng-container [ngTemplateOutlet]="classNameTemp"></ng-container>
                </div>
                <div class="plan-content mb-0">${{ scheduleInfo.price ?? constants.defaultAmountPayableForIntroductoryClasses }}</div>
              </div>

              <div class="space-between discount-row mb-4" *ngIf="!showDiscountField">
                @if (currentUser?.userRoleId === constants.roleIds.CLIENT) {
                  @if (currentUser?.summerCampDiscountPercentage && scheduleInfo.classType === classTypes.SUMMER_CAMP) {
                    <div class="sub-title">Plus/Premium Member Discount ({{ currentUser?.summerCampDiscountPercentage }}%)</div>
                  }
                  @else {
                    <div class="sub-title">Discount</div>
                  }
                }@else {
                <div class="sub-title mb-3 discount-toggle" (click)="showDiscountField = true">
                  <span class="discount-link">Apply discount</span>
                </div>
                }
                @if (scheduleInfo && scheduleInfo.price && currentUser && currentUser.summerCampDiscountPercentage && scheduleInfo.classType === classTypes.SUMMER_CAMP) {
                  <div class="plan-content">-${{ discountAmount | number : '1.2-2' }}</div>
                }
                @else {
                  <div class="plan-content">$0.00</div>
                }
              </div>

              <div class="space-between discount-row mb-4" *ngIf="showDiscountField">
                <div class="sub-title mb-3 discount-input-container">
                  <mat-form-field>
                    <mat-label>Discount Amount</mat-label>
                    <input
                      matInput
                      type="text"
                      pattern="[0-9]*\.?[0-9]*"
                      [(ngModel)]="discountAmount"
                      (ngModelChange)="onDiscountChange()"
                      (keydown)="preventInvalidInput($event)"
                      placeholder="0.00"
                    />
                    <span matPrefix>$&nbsp;</span>
                    <button mat-icon-button matSuffix (click)="clearDiscount()" [attr.aria-label]="'Clear discount'" type="button">
                      <mat-icon>close</mat-icon>
                    </button>
                  </mat-form-field>
                  <div *ngIf="discountError" class="error-message">{{ discountError }}</div>
                </div>
                <div class="plan-content">${{ discountAmount | number : '1.2-2' }}</div>
              </div>

              <!-- to be use for recurring Registration Fee -->
              <!-- <div class="space-between mb-4">
                <div class="sub-title">Registration Fee</div>
                <div class="plan-content mb-0">${{ registrationFee | number:'1.2-2' }}</div>
              </div> -->

              <!-- Security Deposit -->
              <!-- to be use for recurring <div class="space-between mb-4">
                <div class="sub-title">Security Deposit</div>
                <div class="plan-content mb-0">${{ securityDeposit | number:'1.2-2' }}</div>
              </div> -->

              <div class="dotted-divider"></div>
              <div class="space-between">
                <div class="sub-title text-black">Total Payable Amount</div>
                <div class="plan-content mb-0">${{ totalPayableAmount | number : '1.2-2' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="schedule-information-title">
      <div>Add Payment Details</div>
    </div>
    <div>
      <app-payment-methods screen="book-introductory-lesson" [isPaymentFailed]="isRePayment" [showDefaultPaymentBtn]="false" (closeSideNav)="onCloseModal()"></app-payment-methods>
    </div>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>

<ng-template #classNameTemp>
  @if (isScheduleMakeUpLesson) {
  {{ schedulerService.getLessonType(scheduleInfo.lessonType) }} {{ scheduleInfo.passName }} ({{ scheduleInfo.duration }}) } @else { @switch
  (scheduleInfo.classType) { @case (classTypes.GROUP_CLASS) { {{ scheduleInfo.groupClassName }} ({{ scheduleInfo.duration }}) } @case
  (classTypes.ENSEMBLE_CLASS) {
  {{ scheduleInfo.ensembleClassName }}
  } @case (classTypes.SUMMER_CAMP) {
  {{ scheduleInfo.campName | titlecase }}
  } @case (classTypes.ENSEMBLE_CLASS) {
  {{ scheduleInfo.ensembleClassName | titlecase }} ({{ scheduleInfo.duration }}) } @default {
  {{ schedulerService.getLessonType(scheduleInfo.lessonType) }} {{ getInstrumentNameFromValue(scheduleInfo.instrumentId) }} Lesson ({{
    scheduleInfo.skillType
  }}, {{ schedulerService.getAgeLabelFromValue(scheduleInfo.childAge) }}) } } }
</ng-template>
