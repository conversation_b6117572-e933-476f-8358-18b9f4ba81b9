import { NgModule, inject } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ROUTER_PATHS } from '../shared/constants';
import { NoAuthGuard } from '../shared/guards';

const routes: Routes = [
  {
    path: '',
    redirectTo: ROUTER_PATHS.launchPage,
    pathMatch: 'full'
  },
  {
    path: ROUTER_PATHS.launchPage,
    loadComponent: async () => (await import('../launch-page/launch-page.component')).LaunchPageComponent,
    data: {
      pageTitle: 'Launch Page'
    },
    canMatch: [() => inject(NoAuthGuard).canMatch()]
  },
  {
    path: ROUTER_PATHS.auth.login,
    loadComponent: async () => (await import('./pages/login/login.component')).LoginComponent,
    data: {
      pageTitle: 'Login'
    },
    canMatch: [() => inject(NoAuthGuard).canMatch()]
  },
  {
    path: `${ROUTER_PATHS.auth.forgotPassword.root}${ROUTER_PATHS.root}${ROUTER_PATHS.auth.forgotPassword.init}`,
    loadComponent: async () =>
      (await import('./pages/forgot-password-init/forgot-password-init.component')).ForgotPasswordInitComponent,
    data: {
      pageTitle: 'Forgot Password'
    },
    canMatch: [() => inject(NoAuthGuard).canMatch()]
  },
  {
    path: `${ROUTER_PATHS.auth.forgotPassword.root}${ROUTER_PATHS.root}${ROUTER_PATHS.auth.forgotPassword.finish}`,
    loadComponent: async () =>
      (await import('./pages/forgot-password-finish/forgot-password-finish.component')).ForgotPasswordFinishComponent,
    data: {
      pageTitle: 'Reset Password'
    },
    canMatch: [() => inject(NoAuthGuard).canMatch()]
  },
  {
    path: ROUTER_PATHS.auth.signUp,
    loadComponent: async () => (await import('./pages/sign-up/sign-up.component')).SignUpComponent,
    data: {
      pageTitle: 'Sign Up'
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AuthRoutingModule {}
