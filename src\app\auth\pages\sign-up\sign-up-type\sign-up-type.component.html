<div class="sign-up-type-main-wrapper">
  <form [formGroup]="signUpTypeFormGroup">
    <div class="row mt-20">
      <ng-container *ngIf="!isAddDependent">
        <mat-form-field class="col-sm-12 col-md-6">
          <mat-label>First Name</mat-label>
          <input matInput formControlName="firstName" />
          <mat-error>
            <app-error-messages [control]="signUpTypeFormGroup.controls.firstName"></app-error-messages>
          </mat-error>
        </mat-form-field>
        <mat-form-field class="col-sm-12 col-md-6">
          <mat-label>Last Name</mat-label>
          <input matInput formControlName="lastName" />
          <mat-error>
            <app-error-messages [control]="signUpTypeFormGroup.controls.lastName"></app-error-messages>
          </mat-error>
        </mat-form-field>
        <mat-form-field class="col-sm-12 col-md-6">
          <mat-label>Which location do you plan to visit?</mat-label>
          <mat-select formControlName="locationId">
            <mat-option *ngFor="let location of locations" [value]="location.schoolLocations.id">
              {{ location.schoolLocations.locationName }}
            </mat-option>
          </mat-select>
          <mat-error>
            <app-error-messages [control]="signUpTypeFormGroup.controls.locationId"></app-error-messages>
          </mat-error>
        </mat-form-field>
        <mat-form-field class="col-sm-12 col-md-6">
          <mat-label>DOB</mat-label>
          <input matInput [matDatepicker]="picker" [max]="maxDate" (click)="picker.open()" formControlName="dateOfBirth" />
          <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
          <mat-error>
            <app-error-messages [control]="signUpTypeFormGroup.controls.dateOfBirth"></app-error-messages>
          </mat-error>
        </mat-form-field>
        <mat-form-field class="col-sm-12 col-md-6">
          <mat-label>Email</mat-label>
          <input matInput formControlName="emailAddress" />
          <mat-error>
            <app-error-messages [control]="signUpTypeFormGroup.controls.emailAddress"></app-error-messages>
          </mat-error>
        </mat-form-field>
        <mat-form-field class="col-sm-12 col-md-6">
          <mat-label>Phone Number</mat-label>
          <input matInput formControlName="phoneNumber" [mask]="constants.masking.phoneNumberMask" />
          <mat-error>
            <app-error-messages [control]="signUpTypeFormGroup.controls.phoneNumber"></app-error-messages>
          </mat-error>
        </mat-form-field>
        <mat-form-field class="col-12">
          <mat-label>Address</mat-label>
          <textarea matInput formControlName="address"></textarea>
          <mat-error>
              <app-error-messages [control]="signUpTypeFormGroup.controls.address"></app-error-messages>
          </mat-error>
        </mat-form-field>
        <mat-form-field class="col-sm-12 col-md-4">
          <mat-label>State</mat-label>
          <mat-select formControlName="stateId">
            <mat-option *ngFor="let state of states" [value]="state.id">
              {{ state.name }}
            </mat-option>
          </mat-select>
          <mat-error>
            <app-error-messages [control]="signUpTypeFormGroup.controls.stateId"></app-error-messages>
          </mat-error>
        </mat-form-field>
        <mat-form-field class="col-sm-12 col-md-4">
          <mat-label>City</mat-label>
          <input matInput formControlName="city" />
          <mat-error>
            <app-error-messages [control]="signUpTypeFormGroup.controls.city"></app-error-messages>
          </mat-error>
        </mat-form-field>
        <mat-form-field class="col-sm-12 col-md-4">
          <mat-label>Zip Code</mat-label>
          <input matInput formControlName="zipCode" />
          <mat-error>
            <app-error-messages [control]="signUpTypeFormGroup.controls.zipCode"></app-error-messages>
          </mat-error>
        </mat-form-field>
        @if(signUpForOptions.YOUR_CHILD !== signUpTypeFormGroup.controls.dependentId.value) {
          <div class="isNoSubstitute mb-2 mt-0">
            <mat-checkbox formControlName="isNoSubstitute">Don't use a substitute instructor if the assigned instructor is unavailable.</mat-checkbox>
          </div>
        }
        <div class="col-12">
          <label class="form-label required">Who are you signing up at Octopus Music School?</label>
          <div class="btn-typed-options-wrapper">
            <div
              [ngClass]="{
                'btn-typed-option': true,
                active: signUpForOptions.YOURSELF === signUpTypeFormGroup.controls.dependentId.value
              }"
              (click)="setDependentId(signUpForOptions.YOURSELF)"
            >
              Yourself
            </div>
            <div
              [ngClass]="{
                'btn-typed-option': true,
                active: signUpForOptions.YOUR_CHILD === signUpTypeFormGroup.controls.dependentId.value
              }"
              (click)="setDependentId(signUpForOptions.YOUR_CHILD)"
            >
              Your Dependent
            </div>
            <div
              [ngClass]="{
                'btn-typed-option': true,
                active: signUpForOptions.YOURSELF_AND_CHILD === signUpTypeFormGroup.controls.dependentId.value
              }"
              (click)="setDependentId(signUpForOptions.YOURSELF_AND_CHILD)"
            >
              Yourself and Your Dependent
            </div>
          </div>
          <mat-error *ngIf="signUpTypeFormGroup.controls.dependentId.touched || signUpTypeFormGroup.controls.dependentId.dirty">
            <app-error-messages [control]="signUpTypeFormGroup.controls.dependentId"></app-error-messages>
          </mat-error>
        </div>
      </ng-container>
      @if (getDependentInfoFormArray.length) {
      <div class="col-12">
        <div class="child-info-title-wrapper">
          <label class="form-label">Add your Dependent's Information</label>
          <div class="add-child-info-wrapper" (click)="addMoreChild()">Add Another Dependent</div>
        </div>
        <div class="child-info-wrapper" formArrayName="dependentDetails">
          <div class="child-info-form" *ngFor="let child of getDependentInfoFormArray.controls; let i = index">
            <div class="child-info-mobile">
              @if (getDependentInfoFormArray.length > 1) {
              <div class="content">
                {{ 'Dependent ' + (i + 1) }}
              </div>
              <button mat-raised-button class="trash-icon" (click)="deleteDependent(i)">
                <img [src]="constants.staticImages.icons.trash" alt="" />
              </button>
              }
            </div>
            <div class="child-info" [formGroupName]="i">
              <mat-form-field class="content">
                <mat-label> Dependent First Name </mat-label>
                <input matInput formControlName="firstName" />
                <mat-error>
                  <app-error-messages [control]="getDependentInfoFormArray.controls[i].get('firstName')"></app-error-messages>
                </mat-error>
              </mat-form-field>
              <mat-form-field class="content">
                <mat-label> Dependent Last Name </mat-label>
                <input matInput formControlName="lastName" />
                <mat-error>
                  <app-error-messages [control]="getDependentInfoFormArray.controls[i].get('lastName')"></app-error-messages>
                </mat-error>
              </mat-form-field>
              <mat-form-field class="content">
                <mat-label> Dependent DOB </mat-label>
                <input matInput [max]="maxDate" [matDatepicker]="pickerChild" formControlName="dateOfBirth" (click)="pickerChild.open()" />
                <mat-datepicker-toggle matSuffix [for]="pickerChild"></mat-datepicker-toggle>
                <mat-datepicker #pickerChild></mat-datepicker>
                <mat-error>
                  <app-error-messages [control]="getDependentInfoFormArray.controls[i].get('dateOfBirth')"></app-error-messages>
                </mat-error>
              </mat-form-field>
              @if (!!!signUpTypeFormGroup.controls.allAttendedAtSameLocation.value) {
              <mat-form-field class="content">
                <mat-label>Location</mat-label>
                <mat-select formControlName="locationId">
                  <mat-option *ngFor="let location of locations" [value]="location.schoolLocations.id">
                    {{ location.schoolLocations.locationName }}
                  </mat-option>
                </mat-select>
                <mat-error>
                  <app-error-messages [control]="getDependentInfoFormArray.controls[i].get('locationId')"></app-error-messages>
                </mat-error>
              </mat-form-field>
              }
              <mat-form-field class="content">
                <mat-label>Substitute</mat-label>
                <mat-select formControlName="isNoSubstitute">
                  <mat-option [value]="false">Allow Substitute instructor</mat-option>
                  <mat-option [value]="true">Don't Allow Substitute</mat-option>
                </mat-select>
                <mat-error>
                  <app-error-messages [control]="getDependentInfoFormArray.controls[i].get('isNoSubstitute')"></app-error-messages>
                </mat-error>
              </mat-form-field>
              @if (getDependentInfoFormArray.length > 1) {
              <button
                mat-raised-button
                class="trash-icon mb-4"
                (click)="deleteDependent(i, getDependentInfoFormArray.controls[i].get('id')?.value)"
              >
                <img [src]="constants.staticImages.icons.trash" alt="" />
              </button>
              }
            </div>
          </div>
        </div>
        <div class="col-12">
          <mat-checkbox formControlName="allAttendedAtSameLocation" id="allAttendedAtSameLocation">
            All will attend the same location</mat-checkbox
          >
        </div>
      </div>
      }
    </div>
  </form>
</div>
